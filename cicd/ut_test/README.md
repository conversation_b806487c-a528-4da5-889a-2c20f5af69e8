# 単体テスト実行ガイド

## 📋 概要

このディレクトリには、自動デプロイシステムの単体テストが含まれています。
時刻ベース管理方式への移行に対応したテストケースを提供します。

## 🎯 テスト対象システム

- **デプロイ管理方式**: 時刻ベース管理（deploy_baseline_time）
- **Parameter Store**: `/dlpf/{env}/deploy-baseline-time`
- **JSON構造**: `{"deploy_baseline_time": "2025-05-27T10:30:00Z"}`

## 📁 テストファイル一覧

### 🔴 **時刻ベース管理対応テスト（高優先度）**

| ファイル名 | 説明 | 実行時間 | 依存関係 |
|-----------|------|---------|---------|
| `test_get_merged_prs_local.sh` | PR取得スクリプトの基本動作確認 | ~30秒 | なし |
| `test_build_json_parameter.sh` | Parameter Store JSON解析テスト | ~60秒 | GitHub認証 |
| `test_build_initial_deployment.sh` | 初回デプロイ処理テスト | ~60秒 | GitHub認証 |
| `test_build_parameter_store_error.sh` | Parameter Storeエラー処理テスト | ~30秒 | なし |

### 🟡 **その他のテスト（中優先度）**

| ファイル名 | 説明 | 実行時間 | 依存関係 |
|-----------|------|---------|---------|
| `test_get_merged_prs.sh` | PR取得スクリプト（CodeBuild環境） | ~120秒 | GitHub認証 |
| `test_github_auth.sh` | GitHub App認証テスト | ~60秒 | AWS認証 |
| `test_github_app_info.sh` | GitHub App情報取得テスト | ~30秒 | AWS認証 |
| `test_lambda_deploy.sh` | Lambda関数デプロイテスト | ~90秒 | AWS認証 |
| `test_build_empty_changes.sh` | 変更なし時の処理テスト | ~60秒 | GitHub認証 |
| `test-sns-notification.sh` | SNS通知機能テスト | ~30秒 | AWS認証 |

## 🔗 **重要: GitHub認証の依存関係**

### **認証フロー**
```
AWS Secrets Manager → GitHub App JWT → GitHub Installation Token
```

### **依存関係チェーン**
```
cicd/scripts/auth/github-auth.sh (基盤認証スクリプト)
    ↓ INSTALLATION_TOKEN生成・出力
test_build_json_parameter.sh
test_build_initial_deployment.sh
test_get_merged_prs.sh
test_build_parameter_store_error.sh
    ↓ GITHUB_TOKEN環境変数として使用
各テストでのGitHub API呼び出し
```

⚠️ **重要**: GitHub認証が必要なテストは、すべて `cicd/scripts/auth/github-auth.sh` に依存しています。このスクリプトが正常に動作することが前提条件です。

## 🚀 **推奨実行順序**

### **Phase 1: 基本動作確認（認証不要）**

```bash
# 1. PR取得スクリプトの基本動作確認
./test_get_merged_prs_local.sh

# 2. Parameter Storeエラー処理確認
./test_build_parameter_store_error.sh
```

### **Phase 2: GitHub認証基盤確認**

```bash
# 3. GitHub App認証確認（認証フローの動作確認）
./test_github_auth.sh

# 4. GitHub App情報取得確認（JWT認証の動作確認）
./test_github_app_info.sh
```

⚠️ **注意**: Phase 3以降のテストは `cicd/scripts/auth/github-auth.sh` に直接依存します。Phase 2のテストが失敗しても、基盤スクリプト自体が正常であればPhase 3は実行可能です。

### **Phase 3: 時刻ベース管理テスト**

```bash
# 5. Parameter Store JSON解析テスト
./test_build_json_parameter.sh

# 6. 初回デプロイ処理テスト
./test_build_initial_deployment.sh

# 7. PR取得スクリプト（CodeBuild環境）
./test_get_merged_prs.sh
```

### **Phase 4: 統合機能テスト**

```bash
# 8. Lambda関数デプロイテスト
./test_lambda_deploy.sh

# 9. 変更なし時の処理テスト
./test_build_empty_changes.sh

# 10. SNS通知機能テスト
./test-sns-notification.sh
```

## ⚙️ **実行前の準備**

### **必要な環境変数**

```bash
# AWS認証（必須）
export AWS_PROFILE=your-profile
# または
export AWS_ACCESS_KEY_ID=your-access-key
export AWS_SECRET_ACCESS_KEY=your-secret-key

# GitHub認証（自動取得 - 手動設定不要）
# ⚠️ 注意: GITHUB_TOKENは test_github_auth.sh が自動生成します
# 手動設定は不要です（むしろ設定すると認証フローが混乱する可能性があります）

# デバッグモード（オプション）
export DEBUG_MODE=true
```

### **GitHub認証の仕組み**

```bash
# 各テストファイルでの実際の使用方法
INSTALLATION_TOKEN=$(./cicd/scripts/auth/github-auth.sh github-app-credentials | tail -n 1)
export GITHUB_TOKEN="$INSTALLATION_TOKEN"

# github-auth.sh 内部の処理フロー:
# 1. AWS Secrets Manager からGitHub App情報を取得
SECRET_JSON=$(aws secretsmanager get-secret-value --secret-id "github-app-credentials")

# 2. GitHub App JWTを生成
JWT=$(./cicd/scripts/auth/generate_jwt.sh "$APP_ID" "$PRIVATE_KEY")

# 3. GitHub Installation Tokenを取得・出力
INSTALLATION_TOKEN=$(curl -X POST -H "Authorization: Bearer ${JWT}" \
  "https://api.github.com/app/installations/${INSTALLATION_ID}/access_tokens" | jq -r '.token')
echo "$INSTALLATION_TOKEN"  # 最後の行として出力
```

### **必要なツール**

```bash
# 必須ツール
- aws-cli (v2.x)
- jq (v1.6+)
- gh (GitHub CLI v2.x)
- bash (v4.0+)

# インストール確認
aws --version
jq --version
gh --version
bash --version
```

## 🧪 **テスト実行方法**

### **個別実行**

```bash
cd cicd/ut_test

# 実行権限付与
chmod +x test_get_merged_prs_local.sh

# テスト実行
./test_get_merged_prs_local.sh
```

### **一括実行（Phase別）**

```bash
# Phase 1: 基本動作確認（認証不要）
for test in test_get_merged_prs_local.sh test_build_parameter_store_error.sh; do
  echo "=== Running $test ==="
  ./$test
  echo ""
done

# Phase 2: GitHub認証基盤確認
echo "=== Phase 2: GitHub認証基盤確認 ==="
./test_github_auth.sh
./test_github_app_info.sh

# Phase 3: 時刻ベース管理テスト（github-auth.sh依存）
echo "=== Phase 3: 時刻ベース管理テスト ==="
echo "基盤認証スクリプトの動作確認..."
if ./cicd/scripts/auth/github-auth.sh github-app-credentials >/dev/null 2>&1; then
  echo "✅ github-auth.sh 正常 - Phase 3実行可能"
  for test in test_build_json_parameter.sh test_build_initial_deployment.sh test_get_merged_prs.sh; do
    echo "=== Running $test ==="
    ./$test
    echo ""
  done
else
  echo "❌ github-auth.sh 失敗 - Phase 3をスキップ"
  echo "AWS認証とSecrets Manager権限を確認してください"
fi
```

### **全テスト実行**

```bash
# 全テスト実行（時間がかかります）
for test in *.sh; do
  if [[ -x "$test" ]]; then
    echo "=== Running $test ==="
    ./$test
    echo ""
  fi
done
```

## 📊 **テスト結果の確認**

### **成功パターン**

```
✅ Test passed: Error message detected
✅ 成功: 初回デプロイ時刻で適切なエラーメッセージが出力された
✅ Syntax check passed
```

### **失敗パターン**

```
❌ Test failed: Error message not detected
❌ 失敗: 期待されるエラーメッセージが出力されなかった
ERROR: Parameter Storeから値を取得できませんでした
```

## 🔧 **トラブルシューティング**

### **よくある問題**

#### **1. GitHub認証エラー**

```bash
# 問題: AWS Secrets Manager からGitHub App情報を取得できない
# 解決: AWS認証とSecrets Manager権限を確認
aws secretsmanager get-secret-value --secret-id "github-app-credentials"

# 問題: GitHub App JWT生成に失敗
# 解決: Private Keyの形式を確認
./cicd/scripts/auth/generate_jwt.sh "$APP_ID" "$PRIVATE_KEY"

# 問題: GitHub Installation Token取得に失敗
# 解決: GitHub APIへのネットワークアクセスを確認
curl -I https://api.github.com

# ⚠️ 注意: 手動でGITHUB_TOKENを設定しないでください
# システムが自動生成するInstallation Tokenを使用します
```

#### **2. AWS認証エラー**

```bash
# 問題: AWS認証情報が設定されていない
# 解決: AWS CLIで認証設定
aws configure

# 問題: Parameter Storeアクセス権限不足
# 解決: 適切なIAMロール/ポリシーを設定
```

#### **3. 実行権限エラー**

```bash
# 問題: スクリプトに実行権限がない
# 解決: 実行権限を付与
chmod +x *.sh
```

#### **4. 依存ツールエラー**

```bash
# 問題: jqコマンドが見つからない
# 解決: jqをインストール
# Ubuntu/Debian
sudo apt-get install jq

# macOS
brew install jq

# 問題: GitHub CLIが見つからない
# 解決: GitHub CLIをインストール
# Ubuntu/Debian
sudo apt-get install gh

# macOS
brew install gh
```

## 📝 **テスト結果ログ**

テスト実行時のログは以下の場所に保存されます：

```
/tmp/test_*.log          # 各テストの詳細ログ
/tmp/test_result_*.json  # テスト結果のJSONファイル
```

## 🔄 **継続的インテグレーション**

### **CodeBuild環境での実行**

```bash
# CodeBuild環境では以下の環境変数が自動設定されます
CODEBUILD_SRC_DIR=/codebuild/output/src123456789/src
ENVIRONMENT=dev
DEBUG_MODE=false

# テスト実行
cd $CODEBUILD_SRC_DIR/cicd/ut_test
./test_get_merged_prs_local.sh
```

### **ローカル開発環境での実行**

```bash
# ローカル環境では手動で環境変数を設定
export CODEBUILD_SRC_DIR=$(pwd)
export ENVIRONMENT=dev
export DEBUG_MODE=true

# テスト実行
cd cicd/ut_test
./test_get_merged_prs_local.sh
```

## 📚 **関連ドキュメント**

- [デプロイシステム設計書](../docs/deployment-system.md)
- [運用ガイド](../docs/operation-guide.md)
- [AWSリソース設計書](../docs/aws-resources.md)
- [統合テストガイド](../it_test/README.md)

## 🆕 **変更履歴**

| 日付 | バージョン | 変更内容 |
|------|-----------|---------|
| 2025-05-28 | 1.0 | 時刻ベース管理対応版として新規作成 |

---

**注意**: このテストガイドは時刻ベース管理方式（deploy_baseline_time）に対応しています。
従来のPR番号ベース管理とは異なりますので、ご注意ください。
