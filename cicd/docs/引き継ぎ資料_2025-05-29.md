# 🚀 DLPF自動デプロイシステム 引き継ぎ資料

**作成日**: 2025-05-29
**作成者**: TIS黄
**対象**: 新入りメンバー・引き継ぎ担当者

## 📋 **現在のプロジェクト状況**

### **🎯 プロジェクト概要**
- **プロジェクト名**: DLPF自動デプロイシステム
- **目的**: GitHub PR→CodeBuild→AWS自動デプロイの実現
- **現在の状況**: **本格運用準備完了** ✅
- **主要技術**: AWS CodeBuild, GitHub App認証, CloudFormation, Glue Job

### **📊 進捗状況サマリー**
- **完了率**: 95% (20セクション中19セクション完了)
- **主要機能**: 全て実装完了・テスト済み
- **残課題**: SNS通知機能の最終確認のみ

## 🏗️ **システム構成**

### **主要コンポーネント**
1. **GitHub App認証**: `github-app-credentials` (統一済み)
2. **CodeBuildプロジェクト**: `cdp-dev-dlpf-deploy`
3. **時刻ベース管理**: Parameter Store `/dlpf/dev/deploy-baseline-time`
4. **エラー通知**: SNS `cdp-dev-dlpf-deploy-error-notification`

### **デプロイフロー**
```
GitHub PR Merge → CodeBuild起動 → 時刻ベース変更検出 →
CloudFormation/Glue Job デプロイ → ラベル付与 → 完了
```

## 📚 **必読ドキュメント（優先順）**

### **🔴 最優先（必須）**
1. **[運用ガイド](operation-guide.md)** - 日常運用手順
2. **[デプロイシステム設計書](deployment-system.md)** - システム全体設計
3. **[ToDoList](ToDoList.md)** - 進捗状況・課題管理

### **🟡 重要（理解推奨）**
4. **[AWSリソース設計書](aws-resources.md)** - インフラ構成
5. **[単体テストガイド](../ut_test/README.md)** - テスト実行方法

### **🟢 参考（必要時参照）**
6. **[統合テストガイド](../it_test/README.md)** - 統合テスト
7. **[課題一覧](課題List.md)** - 技術課題詳細

## 🔧 **最近の重要な修正（2025-05-29）**

### **✅ 完了した作業**
1. **命名規則統一**: `github-app-credentials-dev` → `github-app-credentials`
   - 修正ファイル: 11ファイル（ドキュメント4、テスト6、スクリプト1）

2. **GitHub App情報取得テスト修正**:
   - 問題: `curl -v`オプションによる詳細ログ出力で停止
   - 解決: `curl -s --connect-timeout 10`に変更

3. **統合テスト実施**: Phase 1-4 全テスト実行
   - 成功: 10/11テスト
   - 確認済み: 基本機能、認証、時刻管理、統合機能

### **🔄 進行中の作業**
1. **SNS通知機能テスト**: SNS_TOPIC_ARN設定後の最終確認

## 🚨 **緊急時対応**

### **重要な連絡先**
- **プロジェクトリーダー**: TIS黄 (<EMAIL>)
- **インフラチーム**: 二宮さん
- **業務チーム**: 王さん

### **緊急時の対応手順**
1. **デプロイ失敗時**: [運用ガイド](operation-guide.md) 参照
2. **システム障害時**: AWS CloudFormation continue-update-rollback 実行
3. **認証エラー時**: GitHub App認証情報確認

## 🎯 **次のタスク（優先順）**

### **🔴 最優先**
1. **SNS通知機能テスト完了** (20.4)
   - SNS_TOPIC_ARN設定確認
   - エラー通知機能動作確認

2. **システム本格運用準備完了確認** (20.5)
   - 全機能統合テスト最終確認
   - 運用開始判定

### **🟡 中優先**
3. **検証環境自動デプロイ手順書作成** (18.4) - 二宮さん担当
4. **開発環境定時自動実行の仕組み構築** (18.5) - 二宮さん担当

## 🔍 **技術的なポイント**

### **重要な設定値**
```bash
# GitHub App認証
SECRET_NAME="github-app-credentials"

# Parameter Store
DEPLOY_BASELINE_TIME="/dlpf/dev/deploy-baseline-time"

# SNS通知
SNS_TOPIC_ARN="arn:aws:sns:ap-northeast-1:ACCOUNT:cdp-dev-dlpf-deploy-error-notification"
```

### **よくある問題と解決方法**
1. **JWT認証エラー**: 時刻同期確認、秘密鍵形式確認
2. **Parameter Store エラー**: JSON形式確認、権限確認
3. **CloudFormation失敗**: continue-update-rollback実行

## 📝 **作業時の注意事項**

### **🚨 絶対に守ること**
1. **直接commitは禁止**: 必ずブランチ作成→PR→マージ
2. **cfn_deploy.sh必須**: 手動デプロイ禁止
3. **テスト実行必須**: 変更前に単体テスト実行

### **💡 推奨事項**
1. **DEBUG_MODE=true**: 開発時はデバッグモード有効
2. **WSLでローカルテスト**: AWS環境前にローカル確認
3. **日本語コミット**: PR作成時は日本語使用

## 🔄 **定期作業**

### **日次**
- CodeBuildログ確認
- エラー通知確認

### **週次**
- ToDoList進捗更新
- テスト実行状況確認

### **月次**
- AWSリソース使用量確認
- セキュリティ設定見直し

## 🎓 **新入り向け学習ロードマップ**

### **📅 第1週: 基礎理解**
1. **Day 1-2**: 必読ドキュメント読破
   - 運用ガイド、デプロイシステム設計書
2. **Day 3-4**: 環境構築・認証設定
   - AWS CLI設定、GitHub App認証確認
3. **Day 5**: 単体テスト実行
   - Phase 1-2テスト実行・理解

### **📅 第2週: 実践習得**
1. **Day 1-2**: デプロイ実行体験
   - テスト用PRでのデプロイ実行
2. **Day 3-4**: トラブルシューティング
   - エラー対応手順の実践
3. **Day 5**: 統合テスト実行
   - Phase 3-4テスト実行・分析

### **📅 第3週: 運用習熟**
1. **独立してデプロイ作業実行**
2. **ドキュメント更新作業**
3. **改善提案・実装**

## 🔧 **開発環境セットアップ**

### **必要なツール**
```bash
# AWS CLI v2
aws --version

# GitHub CLI
gh --version

# jq (JSON処理)
jq --version

# 基本ツール
bash --version
git --version
```

### **環境変数設定**
```bash
# AWS認証
export AWS_PROFILE=your-profile

# デバッグモード (開発時)
export DEBUG_MODE=true

# 作業ディレクトリ
cd /path/to/tis-dlpf-app
```

## 📚 **参考資料・外部リンク**

### **AWS公式ドキュメント**
- [AWS CodeBuild ユーザーガイド](https://docs.aws.amazon.com/codebuild/)
- [AWS CloudFormation ユーザーガイド](https://docs.aws.amazon.com/cloudformation/)
- [AWS Secrets Manager ユーザーガイド](https://docs.aws.amazon.com/secretsmanager/)

### **GitHub公式ドキュメント**
- [GitHub Apps ドキュメント](https://docs.github.com/en/apps)
- [GitHub API リファレンス](https://docs.github.com/en/rest)

### **社内リソース**
- TISインフラチーム Wiki
- DLPF プロジェクト Redmine
- 社内 AWS アカウント管理システム

---

**📞 質問・相談**: 不明点があれば遠慮なく TIS黄 (<EMAIL>) まで連絡してください。
