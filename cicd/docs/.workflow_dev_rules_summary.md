# 🚀 CI/CDパイプライン構築業務 Workflow・ルール サマリ

**最終更新**: 2025-05-29
**作成者**: TIS黄
**用途**: 開発業務の標準ワークフロー・ルール集

## 🔄 **基本開発ワークフロー**

### **標準開発フロー**
```mermaid
flowchart LR
    A[現在ブランチで修正] --> B[全変更コミット]
    B --> C[developへPR作成]
    C --> D[セルフレビュー]
    D --> E[PRマージ]
    E --> F[CodeBuildデプロイ]
    F --> G[CodeBuild実行確認]
```

### **ブランチ管理ルール**
- **develop**: 開発環境用（自動デプロイ対象）
- **release**: 検証環境用
- **master**: 本番環境用
- **feature/***: 機能開発用（developから分岐）

### **必須ルール**
- ✅ **直接developコミット禁止** - 必ず別ブランチでPR作成
- ✅ **Squash Merge必須** - PRマージ後はブランチ削除・再作成
- ✅ **セルフレビュー必須** - マージ前に必ず内容確認

## 🚫 **重要な禁止事項**

### **ファイル管理**
```bash
# ❌ コミット禁止ファイル（Augment会話記録）
cicd/docs/タスク進捗サマリー_*.md
cicd/docs/引き継ぎチェックリスト_*.md
cicd/docs/引き継ぎ資料_*.md
cicd/docs/.workflow_dev_rules_summary.md
```

### **デプロイルール**
- ❌ **cfn_deploy.sh以外でのデプロイ禁止** - チーム統一ルール
- ❌ **Parameter Store直接編集禁止** - 自動更新のみ
- ❌ **手動権限変更禁止** - CloudFormationテンプレート経由のみ

### **依存関係管理**
- ❌ **package.json等の直接編集禁止** - 必ずパッケージマネージャー使用
- ❌ **requirements.txt直接編集禁止** - pip/poetry等を使用

## 📊 **AWS リソース管理ルール**

### **命名規則**
```bash
# 環境別プレフィックス
dlpf-dev-*    # 開発環境
dlpf-stg-*    # 検証環境
dlpf-prd-*    # 本番環境

# CodeBuildプロジェクト
cdp-{env}-dlpf-deploy

# cicdテンプレート（スタック名重複回避）
cicd-{resource-type}-{name}
```

### **CloudFormation管理**
- ✅ **リソース種別分離** - Parameter Store、SNS、CodeBuildを分離
- ✅ **DeletionPolicy: Retain** - 重要データ保護
- ✅ **continue-update-rollback優先** - delete-and-recreate回避

### **権限管理**
- ✅ **チーム権限付与** - group-dev-dlpf-appdev全体
- ✅ **最小権限原則** - 必要最小限の権限のみ
- ✅ **VPC内実行** - 固定IP要件対応

## 🔧 **技術的制約・要件**

### **GitHub統合**
```bash
# 認証方式
- GitHub App認証: PR情報取得用
- CodeConnections: リポジトリクローン用
- 両方併用が必要（異なる目的）

# 統一認証情報名
SECRET_NAME="github-app-credentials"
```

### **環境設定**
```bash
# Python実行環境
Python 3.9

# CodeBuild設定
BUILD_GENERAL1_MEDIUM (7GB RAM)
最大同時実行数: 1
タイムアウト: 60分
```

### **時刻ベース管理**
```json
{
  "deploy_baseline_time": "2025-05-27T10:30:00Z"
}
```
- ✅ **PR追越し問題解決** - PR番号依存排除
- ✅ **直感的運用** - 時刻ベース管理
- ✅ **手動調整可能** - 緊急時対応

## 📝 **ドキュメント管理ルール**

### **言語使用ルール**
- ✅ **コメント**: 英語（UTF-8エラー回避）
- ✅ **コミットメッセージ**: 日本語
- ✅ **PR作成**: 日本語
- ✅ **設計文書**: 日本語（既存標準に合わせる）

### **必須更新対象**
```bash
cicd/docs/ToDoList.md          # タスク管理
cicd/docs/issues-list.md       # 課題管理
cicd/docs/deployment-system.md # 設計書
cicd/docs/operation-guide.md   # 運用ガイド
```

### **タスク管理**
- ✅ **ToDoList**: 実行可能タスク
- ✅ **issues-list**: 分析が必要な課題
- ✅ **進捗サマリ**: 定期的な状況報告

## 🧪 **テスト・品質管理**

### **必須テスト**
```bash
# Phase 1: 基本動作確認
test_get_merged_prs_local.sh
test_build_parameter_store_error.sh

# Phase 2: GitHub認証確認
test_github_auth.sh
test_github_app_info.sh

# Phase 3: 時刻ベース管理確認
test_build_json_parameter.sh
test_build_initial_deployment.sh
test_get_merged_prs.sh

# Phase 4: 統合機能確認
test_lambda_deploy.sh
test_build_empty_changes.sh
test_sns_notification.sh
```

### **エラーハンドリング**
- ✅ **set -e使用** - エラー時即座停止
- ✅ **リトライ機能** - 一時的障害対応
- ✅ **詳細ログ出力** - トラブルシューティング用

### **通知システム**
- ✅ **開発環境のみSNS通知** - エラー時メール送信
- ✅ **stdout/stderr分離** - 結果とログの明確な分離

## 🚀 **運用・保守ルール**

### **デプロイ実行**
```bash
# 推奨実行方法
1. AWSコンソール手動実行（推奨）
2. deploy.shスクリプト実行

# 緊急時対応
- 特定時刻指定デプロイ
- DRY-RUNモードでの事前確認
```

### **障害対応**
- ✅ **continue-update-rollback優先** - データ保護重視
- ✅ **Parameter Store値保護** - 手動リセット禁止
- ✅ **詳細ログ確認** - CloudWatch Logs活用

### **環境管理**
```bash
# 環境別責任
dev: 開発チーム管理
stg: 手動デプロイ（手順書作成予定）
prd: 本番環境対応検討中
```

## 📋 **チーム連携ルール**

### **情報共有**
- ✅ **Redmine連携** - チケット番号必須
- ✅ **GitHub Issue連携** - 関連Issue記載
- ✅ **定期報告** - 進捗サマリ作成

### **引き継ぎ**
- ✅ **ToDoList更新** - 最新状況反映
- ✅ **課題リスト管理** - 未解決課題明確化
- ✅ **ドキュメント整備** - 運用ガイド完備

## 🎯 **成功基準**

### **機能要件**
- ✅ PR追越し問題の完全解決
- ✅ 自動デプロイの安定動作
- ✅ エラー通知システムの動作

### **運用要件**
- ✅ 直感的で分かりやすい操作
- ✅ 包括的なドキュメント整備
- ✅ 障害時の迅速な復旧

## 📞 **緊急時連絡先**

### **技術的問題**
- **システム障害**: TIS黄 (<EMAIL>)
- **AWS関連**: インフラチーム 二宮さん
- **GitHub関連**: TIS黄

### **業務的問題**
- **デプロイ承認**: プロジェクトマネージャー
- **業務影響**: 業務チーム 王さん
- **緊急対応**: TIS黄 (24時間対応)

## 🔍 **よくある問題と解決方法**

### **認証関連**
1. **JWT認証エラー**: 時刻同期確認、秘密鍵形式確認
2. **GitHub API制限**: レート制限確認、認証情報確認

### **デプロイ関連**
1. **Parameter Store エラー**: JSON形式確認、権限確認
2. **CloudFormation失敗**: continue-update-rollback実行
3. **CodeBuild権限エラー**: IAM権限確認、CloudFormationテンプレート更新

### **運用関連**
1. **PR追越し問題**: 時刻ベース管理で解決済み
2. **エラー通知未受信**: SNS設定確認、メールアドレス確認

## 🎓 **新入り向け学習ロードマップ**

### **📅 第1週: 基礎理解**
1. **Day 1-2**: 必読ドキュメント読破
   - 運用ガイド、デプロイシステム設計書
2. **Day 3-4**: 環境構築・認証設定
   - AWS CLI設定、GitHub App認証確認
3. **Day 5**: 単体テスト実行
   - Phase 1-2テスト実行・理解

### **📅 第2週: 実践習得**
1. **Day 1-2**: デプロイ実行体験
   - テスト用PRでのデプロイ実行
2. **Day 3-4**: トラブルシューティング
   - エラー対応手順の実践
3. **Day 5**: 統合テスト実行
   - Phase 3-4テスト実行・分析

### **📅 第3週: 運用習熟**
1. **独立してデプロイ作業実行**
2. **ドキュメント更新作業**
3. **改善提案・実装**

## 🔧 **開発環境セットアップ**

### **必要なツール**
```bash
# AWS CLI v2
aws --version

# GitHub CLI
gh --version

# jq (JSON処理)
jq --version

# 基本ツール
bash --version
git --version
```

### **環境変数設定**
```bash
# AWS認証
export AWS_PROFILE=your-profile

# デバッグモード (開発時)
export DEBUG_MODE=true

# 作業ディレクトリ
cd /path/to/tis-dlpf-app
```

## 📊 **現在のプロジェクト状況（2025-05-29時点）**

### **進捗統計**
- **総タスク数**: 131タスク (20セクション)
- **完了タスク**: 125タスク ✅
- **進行中タスク**: 4タスク 🔄
- **完了率**: **95.4%**

### **主要マイルストーン達成状況**
| マイルストーン | 状況 | 完了日 |
|---------------|------|--------|
| 基本デプロイ機能実装 | ✅ | 2025-05-15 |
| GitHub App認証実装 | ✅ | 2025-05-22 |
| 時刻ベース管理実装 | ✅ | 2025-05-28 |
| エラー復旧対応完了 | ✅ | 2025-05-28 |
| 統合テスト完了 | ✅ | 2025-05-29 |
| 本格運用開始 | 🔄 | 2025-05-30予定 |

### **最近の重要な修正**
1. **命名規則統一**: `github-app-credentials-dev` → `github-app-credentials`
2. **GitHub App情報取得テスト修正**: curl -vオプション問題解決
3. **統合テスト実施**: Phase 1-4 全テスト実行（10/11成功）

### **残りタスク（優先順）**
1. **SNS通知機能テスト** (20.4) - 最優先
2. **システム本格運用準備完了確認** (20.5) - 最優先

## 🔄 **定期作業**

### **日次**
- CodeBuildログ確認
- エラー通知確認

### **週次**
- ToDoList進捗更新
- テスト実行状況確認

### **月次**
- AWSリソース使用量確認
- セキュリティ設定見直し

## 📚 **参考資料・外部リンク**

### **AWS公式ドキュメント**
- [AWS CodeBuild ユーザーガイド](https://docs.aws.amazon.com/codebuild/)
- [AWS CloudFormation ユーザーガイド](https://docs.aws.amazon.com/cloudformation/)
- [AWS Secrets Manager ユーザーガイド](https://docs.aws.amazon.com/secretsmanager/)

### **GitHub公式ドキュメント**
- [GitHub Apps ドキュメント](https://docs.github.com/en/apps)
- [GitHub API リファレンス](https://docs.github.com/en/rest)

### **社内リソース**
- TISインフラチーム Wiki
- DLPF プロジェクト Redmine
- 社内 AWS アカウント管理システム

---

**📝 備考**: このサマリーは開発業務の効率化と品質向上を目的としています。不明点があれば遠慮なく相談してください。
