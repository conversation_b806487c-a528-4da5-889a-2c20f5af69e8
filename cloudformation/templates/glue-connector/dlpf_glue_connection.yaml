AWSTemplateFormatVersion: "2010-09-09"
Description: "CloudFormation template to create AWS Glue connections for DLPF"

Parameters:
  Environment:
    Type: String
    Description: Environment name (e.g., dev, stg, prd)

  PrimarySubnetId:
    Type: String
    Description: Primary Subnet ID where the Glue connections will be created

  SecondarySubnetId:
    Type: String
    Description: Secondary Subnet ID where the Glue connections will be created

  SGForGlueId:
    Type: AWS::EC2::SecurityGroup::Id
    Description: SGForGlueId

  SGForDbId:
    Type: AWS::EC2::SecurityGroup::Id
    Description: SGForDbId
  
  SGForFTPId:
    Type: AWS::EC2::SecurityGroup::Id
    Description: SGForFTPId

  SecretName:
    Type: String
    Description: Name of the secret in Secrets Manager containing database credentials
    Default: DLPF_DB_DLPF_BATCH

  InstanceId:
    Type: String
    Description: Instance ID where the JDBC connection will be connected

Resources:
  DLPFGlueJDBCConnection1:
    Type: AWS::Glue::Connection
    Properties:
      CatalogId: !Ref AWS::AccountId
      ConnectionInput:
        Name: !Sub "conn-${Environment}-dlpf-private-01"
        Description: !Sub "JDBC Connection to Aurora database for DLPF in ${Environment} environment(AZ1)"
        ConnectionType: "JDBC"
        ConnectionProperties:
          JDBC_CONNECTION_URL: !Sub "jdbc:postgresql://${InstanceId}:5432/dlpf-${Environment}"
          SECRET_ID: !Ref SecretName
        PhysicalConnectionRequirements:
          SubnetId: !Ref PrimarySubnetId
          SecurityGroupIdList:
            - !Ref SGForGlueId
            - !Ref SGForDbId
            - !Ref SGForFTPId
          AvailabilityZone: !Select [0, !GetAZs ""]
  
  DLPFGlueJDBCConnection2:
    Type: AWS::Glue::Connection
    Properties:
      CatalogId: !Ref AWS::AccountId
      ConnectionInput:
        Name: !Sub "conn-${Environment}-dlpf-private-02"
        Description: !Sub "JDBC Connection to Aurora database for DLPF in ${Environment} environment(AZ2)"
        ConnectionType: "JDBC"
        ConnectionProperties:
          JDBC_CONNECTION_URL: !Sub "jdbc:postgresql://${InstanceId}:5432/dlpf-${Environment}"
          SECRET_ID: !Ref SecretName
        PhysicalConnectionRequirements:
          SubnetId: !Ref SecondarySubnetId
          SecurityGroupIdList:
            - !Ref SGForGlueId
            - !Ref SGForDbId
            - !Ref SGForFTPId
          AvailabilityZone: !Select [1, !GetAZs ""]