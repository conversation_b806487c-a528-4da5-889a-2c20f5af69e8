# スクリプト先頭
from source.glue_deps_handler import setup_glue_dependencies

# 依存関係のセットアップを実行
setup_glue_dependencies()

# SQL実行
import traceback
import boto3
import json
import os

from source.common import initialize_env, get_job_params
from source.glue_logger import G<PERSON><PERSON>ogger
from source.common_util import load_sql_config
from source.db_connector import DbConnector
from typing import Dict, Any
from datetime import datetime

class Constants:
    """定数クラス"""

    # タイムスタンプのデフォルト値
    DEFAULT_TIMESTAMP = datetime(1900, 1, 1, 0, 0, 0)

class GlueJobExecuteSql:
    """SQL実行ジョブ"""

    def __init__(self, jobnet_id: str):
        """
        初期化処理
        Args:
            jobnet_id: ジョブネットID
        """
        self.jobnet_id = jobnet_id
        self.logger = GlueLogger(jobnet_id)

    def connect_db(self, secret_name: str) -> DbConnector:
        """
        データベースに接続
        Args:
            secret_name: シークレット名
        Returns:
            DbConnector: DB接続オブジェクト
        """
        try:
            self.db_connector = DbConnector(self.logger, secret_id=secret_name)
            self.db_connector.connect()
            return self.db_connector
        except BaseException as e:
            self.logger.error_common(f"DB接続エラー: {str(e)}")
            raise

    def execute_query(
        self,
        db_connector: DbConnector,
        query_id: str,
        params: Dict[str, Any],
    ):
        """
        クエリを実行してデータを取得
        Args:
            db_connector: DB接続オブジェクト
            query_id: クエリID
            params: 置換パラメータ
        """
        try:
            # 2.4.4.SQL実行
            main_query = load_sql_config(f"{query_id}.sql")
            self.logger.debug(f"main_query: {main_query}")
            # メインクエリの実行
            result = db_connector.exec(
            main_query,
            values = params
            )
        except Exception as e:
            self.logger.error("E_job_execute_sql_003", msg_values=(query_id,))
            raise

    def execute(self, params: Dict[str, Any]):
        """
        メイン処理
        Args:
            params: 実行パラメータ
        """
        try:
            # 2.4.2.1.開始ログ
            self.logger.info(
                "I_job_execute_sql_001",
            )

            # 2.4.3.DB接続
            self.connect_db(params["secret_name"])
            self.db_connector.begin()

            if "file_id" in params:
                last_sync_timestamp = self.get_last_sync_timestamp(params, self.db_connector)
                self.logger.info("I_job_execute_sql_003", (params["file_id"], last_sync_timestamp))
            if "diff_base_timestamp_query" in params:
                diff_base_timestamp = self.get_diff_base_timestamp(params, self.db_connector)
                self.logger.info("I_job_execute_sql_004", (params["diff_base_timestamp_query"], diff_base_timestamp))

            sql_info_dict = json.loads(params["sql_info"])

            # 2.4.4.SQL実行
            sql_info_array = sql_info_dict["sql_info"]
            for sql_info in sql_info_array:
                if "params" in sql_info and sql_info["params"].get("bucket_name") is None:
                    sql_info["params"].update({"bucket_name": os.environ.get("S3_BUCKET_NAME")})
                else:
                    sql_info["params"] = {"bucket_name": os.environ.get("S3_BUCKET_NAME")}
                if "last_sync_timestamp" in locals():
                    if "params" in sql_info:
                        sql_info["params"].update({"sync_datetime": last_sync_timestamp})
                    else:
                        sql_info["params"] = {"sync_datetime": last_sync_timestamp}
                if "diff_base_timestamp" in locals():
                    if "params" in sql_info:
                        sql_info["params"].update({"diff_base_timestamp": diff_base_timestamp})
                    else:
                        sql_info["params"] = {"diff_base_timestamp": diff_base_timestamp}
                if "params" in sql_info:
                    sql_info["params"].update({"job_schedule_id": self.jobnet_id})
                else:
                    sql_info["params"] = {"job_schedule_id": self.jobnet_id}
                self.execute_query(
                    self.db_connector,
                    sql_info["query_id"],
                    sql_info.get("params"),
                )
            self.db_connector.commit()

            # 2.4.5.終了処理
            self.logger.info("I_job_execute_sql_002")

        except BaseException as e:
            # 2.4.6.例外処理
            self.logger.error("E_job_execute_sql_002", msg_values=(str(e),))
            self.logger.error("E_job_execute_sql_001")
            self.db_connector.rollback()
            raise

        finally:
            if self.db_connector:
                self.db_connector.close()

    def get_diff_base_timestamp(self, params:Dict, db_connector: DbConnector) -> datetime:
        """
        差分基準時刻を取得
        Args:
            db_connector: DB接続オブジェクト
        Returns:
            datetime: 差分基準時刻
            データが存在しない場合は1900-01-01を返す
        """
        try:
            # 差分基準時刻クエリの取得と実行
            query_id = params["diff_base_timestamp_query"]
            query = load_sql_config(f"{query_id}.sql")
            self.logger.debug(f"diff_base_timestamp query: {query}")

            result = db_connector.exec(query)
            row = result.fetchone()
            self.logger.debug(f"diff_base_timestamp result: {row}")

            if not row or row[0] is None:
                # データが存在しない場合は暗黙値を返す
                default_time = Constants.DEFAULT_TIMESTAMP
                self.logger.debug(
                    f"差分基準時刻がありません。デフォルト値を使用します。(default_time={default_time})"
                )
                return default_time
            return row[0]
        except Exception as e:
            self.logger.error_common(f"差分基準時刻取得エラー: {str(e)}")
            raise

    def get_last_sync_timestamp(self, params:Dict, db_connector: DbConnector) -> datetime:
        """
        前回同期済タイムスタンプを取得
        Args:
            db_connector: DB接続オブジェクト
        Returns:
            datetime: 同期タイムスタンプ
            初回実行時は1900-01-01を返す
        """
        try:
            sync_query = load_sql_config("sql_common_select_001.sql")
            sync_params = {
                "job_schedule_id": self.jobnet_id,
                "file_name": params["file_id"],
            }
            result = db_connector.exec(sync_query, sync_params)
            row = result.fetchone()
            self.logger.debug(f"last_sync query result: {row}")

            if row and row[0]:
                self.logger.debug(f"前回同期タイムスタンプ: {row[0]}")
                return row[0]

            # 初回実行時はデフォルト値を返す
            default_time = Constants.DEFAULT_TIMESTAMP
            self.logger.debug(
                f"前回同期タイムスタンプがありません。デフォルト値を使用します。(default_time={default_time})"
            )
            return Constants.DEFAULT_TIMESTAMP

        except Exception as e:
            self.logger.error("E_job_db_to_file_002", ("クエリ実行",))
            raise

def get_params() -> Dict[str, Any]:
    """
    パラメータ取得
    Returns:
    Dict[str,Any]: ジョブ起動パラメータ
    """

    # 2.4.1 パラメータ取得
    params = get_job_params()

    # 必須パラメータのチェック
    required_params = [
        "secret_name",  # Secrets Managerシークレット名
        "sql_info",  # SQL情報
        "jobnet_id",  # ジョブネットID
    ]

    for param in required_params:
        if param not in params:
            raise ValueError(f"Required parameter '{param}' is missing")

    return params

def main():
    """
    メイン関数
    """
    try:
        # 環境変数の初期化
        initialize_env()

        # 2.4.1 パラメータ取得
        params = get_params()

        # ジョブの実行
        job = GlueJobExecuteSql(params["jobnet_id"])
        job.execute(params)

    except Exception as e:
        print(f"Error occurred: {str(e)}")
        raise e


if __name__ == "__main__":
    main()
