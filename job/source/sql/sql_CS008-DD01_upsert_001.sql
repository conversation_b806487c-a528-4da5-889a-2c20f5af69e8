INSERT INTO customer (
        customer_code,
        customer_no,
        customer_group_code,
        last_name,
        first_name,
        last_name_kana,
        first_name_kana,
        login_id,
        email,
        password,
        birth_date,
        sex,
        request_mail_type,
        client_mail_type,
        caution,
        login_datetime,
        login_error_count,
        login_locked_flg,
        customer_status,
        customer_valid_status,
        customer_attribute_reply_date,
        latest_point_acquired_date,
        rest_point,
        temporary_point,
        withdrawal_request_date,
        withdrawal_date,
        auth_secret_key,
        customer_type,
        black_customer_kbn,
        black_reason_kbn,
        black_register_date,
        mail_advisability_flg,
        bd_advisability_flg,
        mail_magazine_flg,
        shipped_mail_flg,
        receipt_to,
        receipt_detail,
        demand_exclude_flg,
        crm_customer_id,
        unity_customer_code,
        unity_datetime,
        niyose_flg,
        shipping_method_kbn,
        ec_login_id,
        guest_flg,
        member_no,
        member_status,
        shortage_declare_flg,
        order_monitor_flg,
        member_memo,
        crm_customer_updated_datetime,
        member_rank,
        order_ng_flg,
        free_only_purchase_flg,
        orm_rowid,
        created_user,
        created_datetime,
        updated_user,
        updated_datetime,
        d_created_user,
        d_created_datetime,
        d_updated_user,
        d_updated_datetime,
        d_version
    )
SELECT customer_code,
    customer_no,
    customer_group_code,
    last_name,
    first_name,
    last_name_kana,
    first_name_kana,
    login_id,
    email,
    password,
    birth_date,
    sex,
    request_mail_type,
    client_mail_type,
    caution,
    login_datetime,
    login_error_count,
    login_locked_flg,
    customer_status,
    customer_valid_status,
    customer_attribute_reply_date,
    latest_point_acquired_date,
    rest_point,
    temporary_point,
    withdrawal_request_date,
    withdrawal_date,
    auth_secret_key,
    customer_type,
    black_customer_kbn,
    black_reason_kbn,
    black_register_date,
    mail_advisability_flg,
    bd_advisability_flg,
    mail_magazine_flg,
    shipped_mail_flg,
    receipt_to,
    receipt_detail,
    demand_exclude_flg,
    crm_customer_id,
    unity_customer_code,
    unity_datetime,
    niyose_flg,
    shipping_method_kbn,
    ec_login_id,
    guest_flg,
    member_no,
    member_status,
    shortage_declare_flg,
    order_monitor_flg,
    member_memo,
    crm_customer_updated_datetime,
    member_rank,
    order_ng_flg,
    free_only_purchase_flg,
    orm_rowid,
    created_user,
    created_datetime,
    updated_user,
    updated_datetime,
    'JN_CS008_DD01_001',
    -- d_created_user
    NOW(),
    -- d_created_datetime
    'JN_CS008_DD01_001',
    -- d_updated_user
    NOW(),
    -- d_updated_datetime
    1 -- d_version
FROM customer_work ON CONFLICT (customer_code) DO
UPDATE
SET customer_no = EXCLUDED.customer_no,
    customer_group_code = EXCLUDED.customer_group_code,
    last_name = EXCLUDED.last_name,
    first_name = EXCLUDED.first_name,
    last_name_kana = EXCLUDED.last_name_kana,
    first_name_kana = EXCLUDED.first_name_kana,
    login_id = EXCLUDED.login_id,
    email = EXCLUDED.email,
    password = EXCLUDED.password,
    birth_date = EXCLUDED.birth_date,
    sex = EXCLUDED.sex,
    request_mail_type = EXCLUDED.request_mail_type,
    client_mail_type = EXCLUDED.client_mail_type,
    caution = EXCLUDED.caution,
    login_datetime = EXCLUDED.login_datetime,
    login_error_count = EXCLUDED.login_error_count,
    login_locked_flg = EXCLUDED.login_locked_flg,
    customer_status = EXCLUDED.customer_status,
    customer_valid_status = EXCLUDED.customer_valid_status,
    customer_attribute_reply_date = EXCLUDED.customer_attribute_reply_date,
    latest_point_acquired_date = EXCLUDED.latest_point_acquired_date,
    rest_point = EXCLUDED.rest_point,
    temporary_point = EXCLUDED.temporary_point,
    withdrawal_request_date = EXCLUDED.withdrawal_request_date,
    withdrawal_date = EXCLUDED.withdrawal_date,
    auth_secret_key = EXCLUDED.auth_secret_key,
    customer_type = EXCLUDED.customer_type,
    black_customer_kbn = EXCLUDED.black_customer_kbn,
    black_reason_kbn = EXCLUDED.black_reason_kbn,
    black_register_date = EXCLUDED.black_register_date,
    mail_advisability_flg = EXCLUDED.mail_advisability_flg,
    bd_advisability_flg = EXCLUDED.bd_advisability_flg,
    mail_magazine_flg = EXCLUDED.mail_magazine_flg,
    shipped_mail_flg = EXCLUDED.shipped_mail_flg,
    receipt_to = EXCLUDED.receipt_to,
    receipt_detail = EXCLUDED.receipt_detail,
    demand_exclude_flg = EXCLUDED.demand_exclude_flg,
    crm_customer_id = EXCLUDED.crm_customer_id,
    unity_customer_code = EXCLUDED.unity_customer_code,
    unity_datetime = EXCLUDED.unity_datetime,
    niyose_flg = EXCLUDED.niyose_flg,
    shipping_method_kbn = EXCLUDED.shipping_method_kbn,
    ec_login_id = EXCLUDED.ec_login_id,
    guest_flg = EXCLUDED.guest_flg,
    member_no = EXCLUDED.member_no,
    member_status = EXCLUDED.member_status,
    shortage_declare_flg = EXCLUDED.shortage_declare_flg,
    order_monitor_flg = EXCLUDED.order_monitor_flg,
    member_memo = EXCLUDED.member_memo,
    crm_customer_updated_datetime = EXCLUDED.crm_customer_updated_datetime,
    member_rank = EXCLUDED.member_rank,
    order_ng_flg = EXCLUDED.order_ng_flg,
    free_only_purchase_flg = EXCLUDED.free_only_purchase_flg,
    orm_rowid = EXCLUDED.orm_rowid,
    created_user = EXCLUDED.created_user,
    created_datetime = EXCLUDED.created_datetime,
    updated_user = EXCLUDED.updated_user,
    updated_datetime = EXCLUDED.updated_datetime,
    d_updated_user = 'JN_CS008_DD01_001',
    d_updated_datetime = NOW(),
    d_version = customer.d_version + 1;
