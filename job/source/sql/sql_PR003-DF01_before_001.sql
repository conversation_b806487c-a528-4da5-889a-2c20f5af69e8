-- sync_timestampのtempに基準日時を入れる
INSERT INTO sync_timestamp
    (job_schedule_id, file_name, sync_datetime, sync_datetime_temp, d_created_user, d_created_datetime, d_updated_user, d_updated_datetime, d_version)
VALUES
    ('JN_PR003-DF01_001', 'txProduct', '1900-01-01 0:00:00', NOW(), 'JN_PR003-DF01_001', NOW(), 'JN_PR003-DF01_001', NOW(), 0)
    ,('JN_PR003-DF01_001', 'txPricebook', '1900-01-01 0:00:00', NOW(), 'JN_PR003-DF01_001', NOW(), 'JN_PR003-DF01_001', NOW(), 0)
ON CONFLICT (job_schedule_id, file_name)
DO UPDATE SET
DO UPDATE SET
  sync_datetime_temp = NOW()
  ,d_updated_user = 'JN_PR003-DF01_001'
  ,d_updated_datetime = NOW();
--ワークテーブルクリア
TRUNCATE TABLE wk_pr003_df01_product_cd_list;
TRUNCATE TABLE wk_pr003_df01_product_linkage_work;
--セット商品の重さ、ネコポス体積率、自宅外受取体積率計算 ワークテーブル
TRUNCATE TABLE wk_pr003_df01_set_commodity_volume_list_work;
--定期便の重さ、ネコポス体積率、自宅外受取体積率計算 ワークテーブル
TRUNCATE TABLE wk_pr003_df01_set_regular_sale_volume_list_list_work;
--1:商品連携マスタの更新日時が前回のジョブ実行日時より後の商品
INSERT INTO
    wk_pr003_df01_product_cd_list (
        MAIL_ORDER_PRODUCT_CD,
        -- データ区分
        DATA_DIV
    )
SELECT
    MAIL_ORDER_PRODUCT_CD,
    '01'
FROM
    mdm.product_linkage
WHERE
    PMS_U_YMD > :sync_datetime;
--2:期間別価格連携マスタの価格適用開始日が本日、
--または更新日時が前回のジョブ実行日時より後の商品
INSERT INTO
    wk_pr003_df01_product_cd_list (MAIL_ORDER_PRODUCT_CD, DATA_DIV)
SELECT
    DISTINCT ON (mdm.product_linkage.MAIL_ORDER_PRODUCT_CD) mdm.product_linkage.MAIL_ORDER_PRODUCT_CD,
    '02'
FROM
    mdm.period_price_linkage
    LEFT JOIN mdm.product_linkage ON mdm.product_linkage.MDM_INTEGRATION_MANAGEMENT_CD = mdm.period_price_linkage.MDM_INTEGRATION_MANAGEMENT_CD
WHERE
    mdm.period_price_linkage.mdm_integration_management_cd_nk IS NOT null
    AND(
        DATE(mdm.period_price_linkage.APPLY_START_DATE) <= current_date
        and DATE(mdm.period_price_linkage.APPLY_START_DATE) >= DATE(:sync_datetime)
        OR (
            mdm.period_price_linkage.PMS_U_YMD > :sync_datetime
        )
    )
    and mdm.product_linkage.MAIL_ORDER_PRODUCT_CD is not null
ORDER BY
    mdm.product_linkage.MAIL_ORDER_PRODUCT_CD,
    mdm.period_price_linkage.APPLY_START_DATE DESC ON CONFLICT (MAIL_ORDER_PRODUCT_CD) DO
UPDATE
SET
    DATA_DIV = '02';
--3:セット商品構成の更新日時が前回のジョブ実行日時より後の商品
INSERT INTO
    wk_pr003_df01_product_cd_list (MAIL_ORDER_PRODUCT_CD, DATA_DIV)
SELECT
    DISTINCT ON (commodity_code) commodity_code,
    '03'
FROM
    set_commodity_composition
WHERE
    set_commodity_composition.d_updated_datetime > :sync_datetime
ORDER BY
    commodity_code,
    d_updated_datetime DESC ON CONFLICT (MAIL_ORDER_PRODUCT_CD) DO
UPDATE
SET
    DATA_DIV = '03';
--4:定期便商品構成の更新日時が前回のジョブ実行日時より後の商品
INSERT INTO
    wk_pr003_df01_product_cd_list (MAIL_ORDER_PRODUCT_CD, DATA_DIV)
select
    distinct (sku_code) sku_code,
    '04'
FROM
    regular_sale_base
WHERE
    d_updated_datetime > :sync_datetime ON CONFLICT (MAIL_ORDER_PRODUCT_CD) DO
UPDATE
SET
    DATA_DIV = '04';
INSERT INTO
    wk_pr003_df01_product_cd_list (MAIL_ORDER_PRODUCT_CD, DATA_DIV)
SELECT
    distinct (regular_sale_base.sku_code) sku_code,
    '04'
FROM
    (
        SELECT
            shop_code,
            regular_sale_code
        FROM
            regular_sale_commodity
        WHERE
            d_updated_datetime > :sync_datetime
    ) as RSC
    JOIN regular_sale_base ON regular_sale_base.shop_code = RSC.shop_code
    AND regular_sale_base.regular_sale_code = RSC.regular_sale_code ON CONFLICT (MAIL_ORDER_PRODUCT_CD) DO
UPDATE
SET
    DATA_DIV = '04';
--MAIL_ORDER_PRODUCT_CDによりデータをワークテーブルに保存
INSERT INTO
    wk_pr003_df01_product_linkage_work (
        MDM_INTEGRATION_MANAGEMENT_CD,
        MAIL_ORDER_PRODUCT_CD,
        WEB_PRODUCT_NAME,
        tax_class_id,
        REPRESENTATIVE_PRODUCT_CD,
        REGISTRATION_NAME,
        CONTENTS,
        CORE_DEPARTMENT,
        DEP,
        JAN,
        PRODUCT_SEGMENT,
        BUSINESS_SEGMENT,
        PRODUCT_CAT,
        LGROUP,
        MGROUP,
        SGROUP,
        DGROUP,
        PRODUCT_TYPE,
        WEB,
        ORDER_PER_ORDER_MAX,
        WEIGHT,
        COLOR_NAME,
        COLOR_CD,
        SIZE_NAME,
        SIZE_CD,
        MAIL_DELIVERY_FLG,
        NEKOPOSU_VOLUME_RATE,
        OUTSIDE_HOME_RECEIVE_SERVICE_FLG,
        OUTSIDE_HOME_VOLUME_RATE,
        COMPANY_SALES_BUY_FLG,
        SET_COMPOSITION_FLG,
        BEFORE_RENEWAL_PRODUCT_NO,
        SHAPE_NAME,
        SHAPE_CD,
        SEASON,
        USE_POINT_CNT,
        SALES_CHANNEL_1_SALE_START_DATE,
        SALES_CHANNEL_1_SALE_END_DATE,
        PRODUCT_SERIES,
        SET_PRODUCT_FLG,
        PREFERENTIAL_PRODUCT_FLG,
        txInventoryProductID,
        BUTTOBI_SUBSC_BUNDLE_YN,
        CORE_PRODUCT_NAME,
        DATA_DIV
    )
select
    mdm.product_linkage.MDM_INTEGRATION_MANAGEMENT_CD,
    wk_pr003_df01_product_cd_list.MAIL_ORDER_PRODUCT_CD,
    mdm.product_linkage.WEB_PRODUCT_NAME,
    0,
    mdm.product_linkage.REPRESENTATIVE_PRODUCT_CD,
    mdm.product_linkage.REGISTRATION_NAME,
    mdm.product_linkage.CONTENTS,
    mdm.product_linkage.CORE_DEPARTMENT,
    mdm.product_linkage.DEP,
    mdm.product_linkage.JAN,
    mdm.product_linkage.PRODUCT_SEGMENT,
    mdm.product_linkage.BUSINESS_SEGMENT,
    mdm.product_linkage.PRODUCT_CAT,
    mdm.product_linkage.LGROUP,
    mdm.product_linkage.MGROUP,
    mdm.product_linkage.SGROUP,
    mdm.product_linkage.DGROUP,
    mdm.product_linkage.PRODUCT_TYPE,
    mdm.product_linkage.WEB,
    mdm.product_linkage.ORDER_PER_ORDER_MAX,
    mdm.product_linkage.WEIGHT,
    mdm.product_linkage.COLOR_NAME,
    mdm.product_linkage.COLOR_CD,
    mdm.product_linkage.SIZE_NAME,
    mdm.product_linkage.SIZE_CD,
    mdm.product_linkage.MAIL_DELIVERY_FLG,
    mdm.product_linkage.NEKOPOSU_VOLUME_RATE,
    mdm.product_linkage.OUTSIDE_HOME_RECEIVE_SERVICE_FLG,
    mdm.product_linkage.OUTSIDE_HOME_VOLUME_RATE,
    mdm.product_linkage.COMPANY_SALES_BUY_FLG,
    mdm.product_linkage.SET_COMPOSITION_FLG,
    mdm.product_linkage.BEFORE_RENEWAL_PRODUCT_NO,
    mdm.product_linkage.SHAPE_NAME,
    mdm.product_linkage.SHAPE_CD,
    mdm.product_linkage.SEASON,
    mdm.product_linkage.USE_POINT_CNT,
    mdm.product_linkage.SALES_CHANNEL_1_SALE_START_DATE,
    mdm.product_linkage.SALES_CHANNEL_1_SALE_END_DATE,
    mdm.product_linkage.PRODUCT_SERIES,
    mdm.product_linkage.SET_PRODUCT_FLG,
    mdm.product_linkage.PREFERENTIAL_PRODUCT_FLG,
    CASE
        WHEN mdm.product_linkage.WAREHOUSE_MANAGEMENT_CD IS NOT NULL
        AND (
            mdm.product_linkage.MAIL_ORDER_PRODUCT_CD IS NULL
            OR mdm.product_linkage.MAIL_ORDER_PRODUCT_CD != mdm.product_linkage.WAREHOUSE_MANAGEMENT_CD
        ) THEN mdm.product_linkage.WAREHOUSE_MANAGEMENT_CD
        ELSE NULL
    END AS txInventoryProductID,
    mdm.product_linkage.BUTTOBI_SUBSC_BUNDLE_YN,
    mdm.product_linkage.CORE_PRODUCT_NAME,
    wk_pr003_df01_product_cd_list.DATA_DIV
FROM
    wk_pr003_df01_product_cd_list
    LEFT JOIN mdm.product_linkage ON wk_pr003_df01_product_cd_list.MAIL_ORDER_PRODUCT_CD = mdm.product_linkage.MAIL_ORDER_PRODUCT_CD
WHERE
    mdm.product_linkage.MDM_INTEGRATION_MANAGEMENT_CD is not NULL;
--セット商品の重さ、ネコポス体積率、自宅外受取体積率
INSERT INTO
    wk_pr003_df01_set_commodity_volume_list_work (
        MAIL_ORDER_PRODUCT_CD,
        WEIGHT,
        NEKOPOSU_VOLUME_RATE,
        OUTSIDE_HOME_VOLUME_RATE
    )
SELECT
    SCCL.MAIL_ORDER_PRODUCT_CD AS MAIL_ORDER_PRODUCT_CD,
    SUM(SCCL.composition_quantity * PL2.WEIGHT) AS WEIGHT,
    SUM(
        SCCL.composition_quantity * PL2.NEKOPOSU_VOLUME_RATE
    ) AS NEKOPOSU_VOLUME_RATE,
    SUM(
        SCCL.composition_quantity * PL2.OUTSIDE_HOME_VOLUME_RATE
    ) AS OUTSIDE_HOME_VOLUME_RATE
FROM
    (
        SELECT
            commodity_code AS MAIL_ORDER_PRODUCT_CD,
            child_commodity_code,
            SUM(composition_quantity) AS composition_quantity
        FROM
            wk_pr003_df01_product_linkage_work
            INNER JOIN set_commodity_composition on wk_pr003_df01_product_linkage_work.mail_order_product_cd = set_commodity_composition.commodity_code
        GROUP BY
            commodity_code,
            child_commodity_code
    ) AS SCCL
    INNER JOIN wk_pr003_df01_product_linkage_work AS PL2 ON SCCL.child_commodity_code = PL2.MAIL_ORDER_PRODUCT_CD
GROUP BY
    SCCL.MAIL_ORDER_PRODUCT_CD;
UPDATE
    wk_pr003_df01_product_linkage_work
SET
    WEIGHT = wk_pr003_df01_set_commodity_volume_list_work.WEIGHT,
    NEKOPOSU_VOLUME_RATE = wk_pr003_df01_set_commodity_volume_list_work.NEKOPOSU_VOLUME_RATE,
    OUTSIDE_HOME_VOLUME_RATE = wk_pr003_df01_set_commodity_volume_list_work.OUTSIDE_HOME_VOLUME_RATE
FROM
    wk_pr003_df01_set_commodity_volume_list_work
where
    wk_pr003_df01_product_linkage_work.MAIL_ORDER_PRODUCT_CD = wk_pr003_df01_set_commodity_volume_list_work.MAIL_ORDER_PRODUCT_CD;
--定期便の重さ、ネコポス体積率、自宅外受取体積率
INSERT INTO
    wk_pr003_df01_set_regular_sale_volume_list_list_work (
        MAIL_ORDER_PRODUCT_CD,
        WEIGHT,
        NEKOPOSU_VOLUME_RATE,
        OUTSIDE_HOME_VOLUME_RATE
    )
SELECT
    RS.MAIL_ORDER_PRODUCT_CD AS MAIL_ORDER_PRODUCT_CD,
    SUM(PL2.WEIGHT) AS WEIGHT,
    SUM(PL2.NEKOPOSU_VOLUME_RATE) AS NEKOPOSU_VOLUME_RATE,
    SUM(PL2.OUTSIDE_HOME_VOLUME_RATE) AS OUTSIDE_HOME_VOLUME_RATE
FROM
    (
        SELECT
            --親商品コード：定期便基本情報のSKUコード 子商品コード：定期便商品構成のSKUコード
            regular_sale_base.sku_code AS MAIL_ORDER_PRODUCT_CD,
            regular_sale_commodity.sku_code
        FROM
            --定期便基本情報、定期便商品構成をJOIN
            regular_sale_base
            JOIN regular_sale_commodity ON regular_sale_base.shop_code = regular_sale_commodity.shop_code
            AND regular_sale_base.regular_sale_code = regular_sale_commodity.regular_sale_code
    ) as RS
    INNER JOIN wk_pr003_df01_product_linkage_work AS PL2 ON RS.sku_code = PL2.MAIL_ORDER_PRODUCT_CD
GROUP BY
    RS.MAIL_ORDER_PRODUCT_CD;
UPDATE
    wk_pr003_df01_product_linkage_work
SET
    WEIGHT = wk_pr003_df01_set_regular_sale_volume_list_list_work.WEIGHT,
    NEKOPOSU_VOLUME_RATE = wk_pr003_df01_set_regular_sale_volume_list_list_work.NEKOPOSU_VOLUME_RATE,
    OUTSIDE_HOME_VOLUME_RATE = wk_pr003_df01_set_regular_sale_volume_list_list_work.OUTSIDE_HOME_VOLUME_RATE
FROM
    wk_pr003_df01_set_regular_sale_volume_list_list_work
WHERE
    wk_pr003_df01_product_linkage_work.MAIL_ORDER_PRODUCT_CD = wk_pr003_df01_set_regular_sale_volume_list_list_work.MAIL_ORDER_PRODUCT_CD;
--税率を設定
UPDATE
    wk_pr003_df01_product_linkage_work
SET
    tax_class_id = CASE
        WHEN tax = 0 THEN 0
        WHEN tax = 1 THEN tax_rate
        ELSE 0
    END
FROM
    (
        SELECT
            DISTINCT ON (
                mdm.period_price_linkage.MDM_INTEGRATION_MANAGEMENT_CD
            ) wk_pr003_df01_product_linkage_work.MAIL_ORDER_PRODUCT_CD,
            mdm.period_price_linkage.TAX as tax,
            mdm.period_price_linkage.TAX_RATE as tax_rate
        FROM
            wk_pr003_df01_product_linkage_work
            LEFT JOIN mdm.period_price_linkage ON wk_pr003_df01_product_linkage_work.MDM_INTEGRATION_MANAGEMENT_CD = mdm.period_price_linkage.MDM_INTEGRATION_MANAGEMENT_CD
        WHERE
            mdm.period_price_linkage.mdm_integration_management_cd_nk IS NOT NULL
            AND mdm.period_price_linkage.APPLY_START_DATE IS NOT NULL
            AND DATE(mdm.period_price_linkage.APPLY_START_DATE) <= current_date
        ORDER BY
            mdm.period_price_linkage.MDM_INTEGRATION_MANAGEMENT_CD,
            mdm.period_price_linkage.APPLY_START_DATE DESC
    ) as ppl
where
    wk_pr003_df01_product_linkage_work.mail_order_product_cd = ppl.MAIL_ORDER_PRODUCT_CD;
--分割番号を設定
WITH temp_parallel_num AS (
    SELECT
        MAIL_ORDER_PRODUCT_CD,
        MOD(
            ROW_NUMBER() OVER (
                ORDER BY
                    MAIL_ORDER_PRODUCT_CD
            ) - 1,
            :split_num
        ) + 1 AS parallel_num
    FROM
        (
            SELECT
                MAIL_ORDER_PRODUCT_CD
            FROM
                wk_pr003_df01_product_linkage_work
        )
)
UPDATE
    wk_pr003_df01_product_linkage_work
SET
    split_num = t.parallel_num
FROM
    temp_parallel_num AS t
WHERE
    wk_pr003_df01_product_linkage_work.MAIL_ORDER_PRODUCT_CD = t.MAIL_ORDER_PRODUCT_CD;