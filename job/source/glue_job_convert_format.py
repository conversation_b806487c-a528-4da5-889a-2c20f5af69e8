#!/usr/bin/env python
# -*- coding: utf-8 -*-
# スクリプト先頭
from source.glue_deps_handler import setup_glue_dependencies

# 依存関係のセットアップを実行
setup_glue_dependencies()

import os
import sys
import tempfile
import boto3
from typing import Dict, Any

from source.common import (
    initialize_env,
    get_job_params,
    retry_function,
    find_s3_prefix,
    find_s3_file
)
from source.common_util import load_yaml_config
from source.glue_logger import GlueLogger
from source.format_converter_factory import FormatConverterFactory


class GlueJobConvertFormat:
    """フォーマット変換ジョブ"""

    def __init__(self, jobnet_id: str):
        """
        初期化処理
        Args:
            jobnet_id: ジョブネットID
        """
        self.jobnet_id = jobnet_id
        self.logger = GlueLogger(jobnet_id)
        self.s3_client = boto3.client("s3")
        self.params = None

    def get_file_from_s3(self, bucket: str, key: str) -> bytes:
        """
        S3からファイルをバイト列として取得（リトライ処理付き）
        Args:
            bucket: S3バケット名
            key: S3オブジェクトキー
        Returns:
            bytes: ファイルのバイト列
        """

        def _get_file() -> bytes:
            response = self.s3_client.get_object(Bucket=bucket, Key=key)
            return response["Body"].read()  # バイト列をそのまま返却

        try:
            return retry_function(
                _get_file,
                retry_limit=int(os.environ.get("S3_RETRY_LIMIT", 3)),
                retry_interval=float(os.environ.get("S3_RETRY_INTERVAL", 1.0)),
                logger=self.logger,
                process_name="S3ファイル取得",
                info_msg_id="I_job_convert_format_003",
                error_msg_id="E_job_convert_format_002",
            )
        except Exception as e:
            self.logger.error_common(f"S3ファイル取得エラー: {str(e)}")
            raise

    def convert_format(self, file_content: bytes) -> str:
        """
        ファイルフォーマットを変換
        Args:
            file_content: 変換元ファイルのバイト列
        Returns:
            str: 変換後のファイル内容
        """
        try:
            # 一時ファイルを作成
            with tempfile.NamedTemporaryFile(mode="wb", delete=False) as temp_file:
                # バイト列を直接書き込み
                temp_file.write(file_content)
                temp_file.flush()

                # ETL設定ファイルを読み込み
                etl_config = f"{self.params['etl_yaml_file_id']}"

                # コンバーターの取得と変換
                converter = FormatConverterFactory.get_converter(
                    self.jobnet_id, etl_config
                )

                try:
                    # 変換実行
                    return converter.execute_conversion(temp_file.name, None)
                finally:
                    # 一時ファイルの削除
                    os.unlink(temp_file.name)

        except Exception as e:
            self.logger.error_common(f"フォーマット変換エラー: {str(e)}")
            raise

    def upload_to_s3(self, content: str, encoding: str):
        """
        S3にファイルをアップロード（リトライ処理付き）
        Args:
            content: ファイル内容
            encoding: 文字エンコーディング
        """

        def _upload():
            actual_path = os.path.join(
                self.params["output_file_dir"],
                self.params["output_file_name"]
            )

            # contentがバイト列の場合はそのまま、文字列の場合はエンコード
            body = content if isinstance(content, bytes) else content.encode(encoding)

            self.s3_client.put_object(
                Bucket=os.environ["S3_BUCKET_NAME"],
                Key=actual_path,
                Body=body
            )

        try:
            retry_function(
                _upload,
                retry_limit=int(os.environ.get("S3_RETRY_LIMIT", 3)),
                retry_interval=float(os.environ.get("S3_RETRY_INTERVAL", 1.0)),
                logger=self.logger,
                process_name="S3ファイル配置",
                info_msg_id="I_job_convert_format_003",
                error_msg_id="E_job_convert_format_002",
            )
        except Exception as e:
            self.logger.error_common(f"S3ファイル配置エラー: {str(e)}")
            raise

    def backup_input_file(self):
        """
        入力ファイルのバックアップ
        """
        if not self.params.get("backup_flag", False):
            return

        def _backup():
            src_key = os.path.join(
                self.params["input_file_dir"], self.params["input_file_name"]
            )
            dest_key = os.path.join(
                self.params["backup_file_dir"], self.params["input_file_name"]
            )

            # バックアップファイルディレクトリのチェック
            if not self.params.get("backup_file_dir"):
                raise ValueError(
                    "バックアップフラグがTrueの場合、バックアップファイルディレクトリは必須です"
                )

            self.s3_client.copy_object(
                Bucket=os.environ["S3_BUCKET_NAME"],
                CopySource={"Bucket": os.environ["S3_BUCKET_NAME"], "Key": src_key},
                Key=dest_key,
            )

        try:
            retry_function(
                _backup,
                retry_limit=int(os.environ.get("S3_RETRY_LIMIT", 3)),
                retry_interval=float(os.environ.get("S3_RETRY_INTERVAL", 1.0)),
                logger=self.logger,
                process_name="インプットファイルバックアップ",
                info_msg_id="I_job_convert_format_003",
                error_msg_id="E_job_convert_format_002",
            )
        except Exception as e:
            self.logger.error_common(f"入力ファイルバックアップエラー: {str(e)}")
            raise

    def delete_input_file(self):
        """
        入力ファイルの削除
        """

        def _delete():
            input_key = os.path.join(
                self.params["input_file_dir"], self.params["input_file_name"]
            )

            self.s3_client.delete_object(
                Bucket=os.environ["S3_BUCKET_NAME"], Key=input_key
            )

        try:
            retry_function(
                _delete,
                retry_limit=int(os.environ.get("S3_RETRY_LIMIT", 3)),
                retry_interval=float(os.environ.get("S3_RETRY_INTERVAL", 1.0)),
                logger=self.logger,
                process_name="インプットファイル削除",
                info_msg_id="I_job_convert_format_003",
                error_msg_id="E_job_convert_format_002",
            )
        except Exception as e:
            self.logger.error_common(f"入力ファイル削除エラー: {str(e)}")
            raise

    def execute(self, params: Dict[str, Any]):
        """
        メイン処理
        Args:
            params: 実行パラメータ
        """
        try:
            self.params = params

            # 必須パラメータのチェック
            required_params = [
                "etl_yaml_file_id",  # ETL設定ファイルID
                "input_file_dir",  # インプットファイルディレクトリ
                "output_file_dir",  # アウトプットファイルディレクトリ
                "input_file_name",  # 入力ファイル名
                "output_file_name",  # 出力ファイル名
                "jobnet_id",  # ジョブネットID
            ]

            # 開始ログ出力
            self.logger.info(
                "I_job_convert_format_001",
                (params.get("input_file_name", "Unknown")),
            )

            # 全ての必須パラメータをチェック
            missing_params = [param for param in required_params if param not in params]

            if missing_params:
                # 全ての欠落パラメータを含むエラーログを出力
                error_message = (
                    f"Required parameters are missing: {', '.join(missing_params)}"
                )
                self.logger.error("E_job_convert_format_002", (error_message,))
                self.logger.error(
                    "E_job_convert_format_001",
                    (params.get("input_file_name", "Unknown"),),
                )
                raise ValueError(error_message)

            # 入力のinput_file_dirにタイムスタンプがついてない場合、s3上一番古いタイムスタンプをついて変数に入れ替える
            input_file_dir_origin= self.params["input_file_dir"]
            input_file_dir = find_s3_prefix(input_file_dir_origin, os.environ["S3_BUCKET_NAME"])[0]
            self.params["input_file_dir"] = input_file_dir
            
            
            # 入力のinput_file_nameに*がついている場合、もっとも古い日付付きのファイル名を検索する
            file_name = self.params["input_file_name"]
            if "*" in file_name:
                file_name = find_s3_file(self.params["input_file_dir"], file_name, os.environ["S3_BUCKET_NAME"])
            self.params["input_file_name"] = file_name
            
            # 入力ファイル・出力ファイルをログに出力
            input_file_path = os.path.join(self.params["input_file_dir"], self.params["input_file_name"])
            output_file_path = os.path.join(self.params["output_file_dir"], self.params["output_file_name"])
            self.logger.info("I_job_convert_format_004", (input_file_path, output_file_path))

            # バックアップフラグがTrueの場合、バックアップディレクトリの必須チェック
            if params.get("backup_flag", False) and not params.get("backup_file_dir"):
                error_message = "backup_file_dir is required when backup_flag is True"
                self.logger.error("E_job_convert_format_002", (error_message,))
                self.logger.error(
                    "E_job_convert_format_001",
                    (params.get("input_file_name", "Unknown"),),
                )
                raise ValueError(error_message)


            # ETL設定ファイルの読み込みと検証
            etl_config = load_yaml_config(
                f"converter/{params['etl_yaml_file_id']}.yaml"
            )
            if not etl_config:
                raise ValueError(
                    f"ETL設定ファイルの読み込みに失敗しました: {params['etl_yaml_file_id']}"
                )

            # S3からバイト列としてファイル取得
            file_content = self.get_file_from_s3(
                os.environ["S3_BUCKET_NAME"],
                os.path.join(params["input_file_dir"], params["input_file_name"]),
            )

            # フォーマット変換
            converted_content = self.convert_format(file_content)

            # S3にファイル配置
            self.upload_to_s3(converted_content, etl_config["common"]["encoding"])

            # 終了ログ出力
            self.logger.info(
                "I_job_convert_format_002",
                (params["input_file_name"],),
            )

        except Exception as e:
            # 例外処理
            self.logger.error("E_job_convert_format_003", (str(e),))
            self.logger.error(
                "E_job_convert_format_001",
                (params.get("input_file_name", "Unknown"),),
            )
            raise

        finally:
            try:
                # バックアップ処理
                self.backup_input_file()

                # 入力ファイル削除
                self.delete_input_file()
            except Exception as e:
                # 例外処理
                self.logger.error("E_job_convert_format_003", (str(e),))
                self.logger.error(
                    "E_job_convert_format_001",
                    (params.get("input_file_name", "Unknown"),),
                )
                raise



def main():
    """メイン関数"""
    try:
        # 環境変数の初期化
        initialize_env()

        # パラメータの取得
        params = get_job_params()

        # 任意パラメータのデフォルト値設定
        params.setdefault("backup_flag", False)

        # ジョブの実行
        job = GlueJobConvertFormat(params.get("jobnet_id", "UNKNOWN"))
        job.execute(params)

    except Exception as e:
        print(f"Error occurred: {str(e)}")
        raise e


if __name__ == "__main__":
    main()
